import GridView from "./grid-view";
import ListView from "./list-view";

interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

type Props = {
    activeBtn: string
    folderData: any
    onFolderNavigate?: (folder: Folder) => void
}

const FoldersFiles = (props: Props) => {

    return (
        <div className={`datacontent_wrap ${props.activeBtn}`}>
            {
                props.activeBtn == "grid" ?
                    <GridView props={props} />
                    :
                    <>
                        <ListView props={props} />
                    </>
            }
        </div>
    )
};

export default FoldersFiles;