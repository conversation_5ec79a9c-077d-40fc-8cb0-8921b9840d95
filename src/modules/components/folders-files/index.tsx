import React from "react";
import GridView from "./grid-view";
import ListView from "./list-view";

interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

interface FoldersFilesProps {
    viewMode: string;
    folderData: any;
    selectedItems: string[];
    onFolderNavigate: (folder: Folder) => void;
    onItemSelect: (itemId: string) => void;
}

const FoldersFiles: React.FC<FoldersFilesProps> = ({
    viewMode,
    folderData,
    selectedItems,
    onFolderNavigate,
    onItemSelect
}) => {
    const folders = folderData?.folders || [];
    const files = folderData?.files || [];

    const handleFolderClick = (folder: Folder) => {
        onFolderNavigate(folder);
    };

    const handleFileClick = (file: any) => {
        // Handle file click - could open preview, download, etc.
        console.log('File clicked:', file);
    };

    const handleItemSelect = (itemId: string) => {
        onItemSelect(itemId);
    };

    return (
        <div className={`datacontent_wrap ${viewMode}`}>
            {viewMode === "grid" ? (
                <GridView
                    folders={folders}
                    files={files}
                    selectedItems={selectedItems}
                    onFolderClick={handleFolderClick}
                    onFileClick={handleFileClick}
                    onItemSelect={handleItemSelect}
                />
            ) : (
                <ListView
                    folders={folders}
                    files={files}
                    selectedItems={selectedItems}
                    onFolderClick={handleFolderClick}
                    onFileClick={handleFileClick}
                    onItemSelect={handleItemSelect}
                />
            )}
        </div>
    );
};

export default FoldersFiles;