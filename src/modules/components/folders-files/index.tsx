import GridView from "./grid-view";
import ListView from "./list-view";

type Props = {
    activeBtn: string
    folderData: any
}

const FoldersFiles = (props: Props) => {

    return (
        <div className={`datacontent_wrap ${props.activeBtn}`}>
            {
                props.activeBtn == "grid" ?
                    <GridView props={props} />
                    :
                    <>
                        <ListView props={props} />
                    </>
            }
        </div>
    )
};

export default FoldersFiles;