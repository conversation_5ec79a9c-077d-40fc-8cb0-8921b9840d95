"use client"
import { FileListGallery, FolderInfo, FolderList } from "@/assets/icons";
import { ActionDropdown, OnlyCk } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { useState } from "react";
import { itemsForList, superActionListFile, superActionListFolder } from "../../utils/constant";


const GridView = (props: any) => {
    const [itemSelectedFlag, setitemSelectedFlag] = useState(false);
    console.log('props', props?.props?.folderData?.folders);


    const handleToggle = () => {
        setitemSelectedFlag((current) => !current);
    };
    return (
        <>
            <div className="folder_wrap inner_section">
                <h2>Folders</h2>
                <Row className="folder_list" gutter={{ xs: 12, sm: 24 }}>
                    <Col xs={12} md={8} lg={6} xxl={4}>
                        <div className="icon24_bes_box">
                            <div onClick={()=>{}} className="inner_box">
                                <FolderList />
                                <p>Logo</p>
                            </div>
                            <ActionDropdown
                                items={itemsForList}
                                actionList={superActionListFolder}
                                className="info_action"
                                actionIcon={<FolderInfo />}
                            />
                        </div>
                    </Col>
                </Row>
            </div>
            <div className="files_wrap inner_section">
                <h2>Files</h2>
                <Row className="files_list" gutter={{ xs: 12, sm: 24 }}>
                    <Col xs={12} md={8} lg={6} xxl={4}>
                        <div className={`withmedia_file ${itemSelectedFlag ? "selected" : ""}`} >
                            <div className="item_detail">
                                <div className="inner_box">
                                    <FileListGallery />
                                    <OnlyCk onChange={handleToggle}></OnlyCk>
                                    <p>img1254698.JPG</p>
                                </div>
                                <ActionDropdown
                                    items={itemsForList}
                                    actionList={superActionListFile}
                                    className="info_action"
                                    actionIcon={<FolderInfo />}
                                    overlayClassName="icon_dropdown"
                                />
                            </div>
                            <div className="item_image">
                                <img src="/assets-crm/assets/img/FileImage1.png" alt="" height={100} width={100} />
                            </div>
                        </div>
                    </Col>
                </Row>
            </div>
        </>
    )
};

export default GridView;