"use client"
import { FileListGallery, FolderInfo, FolderList } from "@/assets/icons";
import { ActionDropdown, OnlyCk } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { itemsForList, superActionListFile, superActionListFolderWithShare } from "../../utils/constant";

interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

interface GridViewProps {
    folders: any[];
    files: any[];
    selectedItems: string[];
    onFolderClick: (folder: any) => void;
    onFileClick: (file: any) => void;
    onItemSelect: (itemId: string) => void;
}

const GridView: React.FC<GridViewProps> = ({
    folders,
    files,
    selectedItems,
    onFolderClick,
    onFileClick,
    onItemSelect
}) => {
    console.log('folders', folders);
    console.log('files', files);

    const handleFolderClick = (folder: Folder) => {
        onFolderClick(folder);
    };

    const handleFileClick = (file: any) => {
        onFileClick(file);
    };

    const handleItemSelect = (itemId: string) => {
        onItemSelect(itemId);
    };

    return (
        <>
            <div className="folder_wrap inner_section">
                <h2>Folders</h2>
                <Row className="folder_list" gutter={{ xs: 12, sm: 24 }}>
                    {folders.map((folder) => (
                        <Col key={folder.id} xs={12} md={8} lg={6} xxl={4}>
                            <div className="icon24_bes_box">
                                <div
                                    onClick={() => handleFolderClick(folder)}
                                    className="inner_box"
                                    style={{ cursor: 'pointer' }}
                                >
                                    <FolderList />
                                    <p>{folder.folder_name}</p>
                                </div>
                                <ActionDropdown
                                    items={itemsForList}
                                    actionList={superActionListFolderWithShare}
                                    className="info_action"
                                    actionIcon={<FolderInfo />}
                                />
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
            {files.length > 0 && (
                <div className="files_wrap inner_section">
                    <h2>Files</h2>
                    <Row className="files_list" gutter={{ xs: 12, sm: 24 }}>
                        {files.map((file: any, index: number) => {
                            const isSelected = selectedItems.includes(file.id);
                            return (
                                <Col key={file.id || index} xs={12} md={8} lg={6} xxl={4}>
                                    <div className={`withmedia_file ${isSelected ? "selected" : ""}`}>
                                        <div className="item_detail">
                                            <div
                                                className="inner_box"
                                                onClick={() => handleFileClick(file)}
                                                style={{ cursor: 'pointer' }}
                                            >
                                                <FileListGallery />
                                                <OnlyCk
                                                    checked={isSelected}
                                                    onChange={() => handleItemSelect(file.id)}
                                                />
                                                <p>{file.name || `File ${index + 1}`}</p>
                                            </div>
                                            <ActionDropdown
                                                items={itemsForList}
                                                actionList={superActionListFile}
                                                className="info_action"
                                                actionIcon={<FolderInfo />}
                                                overlayClassName="icon_dropdown"
                                            />
                                        </div>
                                        <div className="item_image">
                                            <img
                                                src={process.env.NEXT_PUBLIC_ASSETS_URL + file.path || "/assets-crm/assets/img/FileImage1.png"}
                                                alt={file.name || "File"}
                                                height={100}
                                                width={100}
                                            />
                                        </div>
                                    </div>
                                </Col>
                            );
                        })}
                    </Row>
                </div>
            )}
        </>
    )
};

export default GridView;