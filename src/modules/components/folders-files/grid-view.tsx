"use client"
import { <PERSON>ListGallery, FolderInfo, FolderList } from "@/assets/icons";
import { ActionDropdown, OnlyCk } from "@dilpesh/kgk-ui-library";
import { Col, Row } from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { itemsForList, superActionListFile, superActionListFolderWithShare } from "../../utils/constant";

interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

const GridView = (props: any) => {
    const [itemSelectedFlag, setitemSelectedFlag] = useState(false);
    const router = useRouter();
    const searchParams = useSearchParams();

    const folders: Folder[] = props?.props?.folderData?.folders || [];
    const files = props?.props?.folderData?.files || [];
    console.log('folders', folders);
    console.log('files', files);

    const handleFolderClick = (folder: Folder) => {
        // Update URL with folder ID
        const params = new URLSearchParams(searchParams.toString());
        params.set('folderId', folder.id);
        router.push(`?${params.toString()}`);

        // Call the onFolderNavigate callback if provided
        if (props?.props?.onFolderNavigate) {
            props.props.onFolderNavigate(folder);
        }
    };

    const handleToggle = () => {
        setitemSelectedFlag((current) => !current);
    };

    return (
        <>
            <div className="folder_wrap inner_section">
                <h2>Folders</h2>
                <Row className="folder_list" gutter={{ xs: 12, sm: 24 }}>
                    {folders.map((folder) => (
                        <Col key={folder.id} xs={12} md={8} lg={6} xxl={4}>
                            <div className="icon24_bes_box">
                                <div
                                    onClick={() => handleFolderClick(folder)}
                                    className="inner_box"
                                    style={{ cursor: 'pointer' }}
                                >
                                    <FolderList />
                                    <p>{folder.name}</p>
                                </div>
                                <ActionDropdown
                                    items={itemsForList}
                                    actionList={superActionListFolderWithShare}
                                    className="info_action"
                                    actionIcon={<FolderInfo />}
                                />
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
            {files.length > 0 && (
                <div className="files_wrap inner_section">
                    <h2>Files</h2>
                    <Row className="files_list" gutter={{ xs: 12, sm: 24 }}>
                        {files.map((file: any, index: number) => (
                            <Col key={file.id || index} xs={12} md={8} lg={6} xxl={4}>
                                <div className={`withmedia_file ${itemSelectedFlag ? "selected" : ""}`} >
                                    <div className="item_detail">
                                        <div className="inner_box">
                                            <FileListGallery />
                                            <OnlyCk onChange={handleToggle}></OnlyCk>
                                            <p>{file.name || `File ${index + 1}`}</p>
                                        </div>
                                        <ActionDropdown
                                            items={itemsForList}
                                            actionList={superActionListFile}
                                            className="info_action"
                                            actionIcon={<FolderInfo />}
                                            overlayClassName="icon_dropdown"
                                        />
                                    </div>
                                    <div className="item_image">
                                        <img
                                            src={file.thumbnail || "/assets-crm/assets/img/FileImage1.png"}
                                            alt={file.name || "File"}
                                            height={100}
                                            width={100}
                                        />
                                    </div>
                                </div>
                            </Col>
                        ))}
                    </Row>
                </div>
            )}
        </>
    )
};

export default GridView;