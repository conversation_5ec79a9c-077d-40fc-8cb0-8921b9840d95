import { FileAssetsIcon, FolderAssetsIcon, VideoAssetsIcon } from "@/assets/icons";
import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import { useState } from "react";
import { assetsListingHead } from "../../utils/constant";
import { getColoumnAssetListing } from "../../utils/utility";

// type Props = {}

const ListView = (props: any) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(7);
    const [recordsTotal, setRecordsTotal] = useState(300);
    const coloumnDefAssetsListing = getColoumnAssetListing({})

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    const transformAssetsData = (apiData: any) => {
        const folders = apiData.folders.map((folder: any) => ({
            nameIcon: <FolderAssetsIcon />,
            name: folder.name,
            ownerIcon: '/assets-crm/assets/img/Owner.png',
            owner: 'Me',
            lastModifiedByIcon: '/assets-crm/assets/img/LastModified.png',
            lastModifiedBy: 'Me',
            lastModifiedOn: folder?.updatedAt ? format(new Date(folder.updatedAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a") : '-',
            fileSize: '-',
        }));

        const files = apiData.files.map((file: any) => {
            const extension = file.file_name.split('.').pop()?.toLowerCase();
            let nameIcon = <FileAssetsIcon />;
            if (['mov', 'mp4', 'avi'].includes(extension || '')) {
                nameIcon = <VideoAssetsIcon />;
            }

            return {
                nameIcon,
                name: file.file_name,
                ownerIcon: '/assets-crm/assets/img/Owner.png',
                owner: 'Me',
                lastModifiedByIcon: '/assets-crm/assets/img/LastModified.png',
                lastModifiedBy: 'Me',
                lastModifiedOn: new Date(file.createdAt).toLocaleString('en-GB', {
                    day: '2-digit', month: 'short', year: 'numeric',
                    hour: '2-digit', minute: '2-digit', hour12: true
                }),
                fileSize: formatFileSize(file.file_size),
            };
        });

        return [...folders, ...files];
    };
    return (
        <>
            <DataTable
                isMultiselect={true}
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={transformAssetsData(props)}
                fixedPosition="first right"
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
            />
        </>
    )
};

export default ListView;