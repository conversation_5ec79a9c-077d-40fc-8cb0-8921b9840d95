"use client"
import { FileAssetsIcon, FolderAssetsIcon, VideoAssetsIcon } from "@/assets/icons";
import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import { useMemo, useState } from "react";
import { assetsListingHead } from "../../utils/constant";
import { getColoumnAssetListing } from "../../utils/utility";

interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

interface File {
    id: string;
    file_name: string;
    file_size: number;
    parent_folder_id: string | null;
    createdAt: string;
    fileRefId: string;
}

interface ListViewProps {
    folders: Folder[];
    files: File[];
    selectedItems: string[];
    onFolderClick: (folder: Folder) => void;
    onFileClick: (file: File) => void;
    onItemSelect: (itemId: string) => void;
}

const ListView: React.FC<ListViewProps> = ({
    folders,
    files,
    selectedItems,
    onFolderClick,
    onFileClick,
    onItemSelect
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(7);

    console.log('folders', folders);
    console.log('files', files);

    const handleFolderClick = (folder: Folder) => {
        onFolderClick(folder);
    };

    const handleFileClick = (file: File) => {
        onFileClick(file);
    };

    const handleItemSelect = (itemId: string) => {
        onItemSelect(itemId);
    };

    // Format file size helper
    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    // Enhanced column definition with click handlers
    const coloumnDefAssetsListing = useMemo(() => {
        const baseColumns = getColoumnAssetListing({});

        // Override the name column to add click handler
        return {
            ...baseColumns,
            name: {
                renderChildren: (item: any) => {
                    const handleClick = () => {
                        if (item.type === 'folder') {
                            handleFolderClick(item.originalData);
                        } else if (item.type === 'file') {
                            handleFileClick(item.originalData);
                        }
                    };

                    return (
                        <div
                            className='icon_name_24_12_bes'
                            onClick={handleClick}
                            style={{ cursor: 'pointer' }}
                        >
                            {item.nameIcon}
                            <p>{item.name}</p>
                        </div>
                    );
                },
            },
        };
    }, []);

    // Transform API data to table format
    const transformedData = useMemo(() => {
        const transformedItems: any[] = [];

        // Add folders to table data
        folders.forEach((folder) => {
            transformedItems.push({
                id: folder.id,
                type: 'folder',
                originalData: folder,
                nameIcon: <FolderAssetsIcon />,
                name: folder.name,
                ownerIcon: '/assets-crm/assets/img/Owner.png',
                owner: 'Me',
                lastModifiedByIcon: '/assets-crm/assets/img/LastModified.png',
                lastModifiedBy: 'Me',
                lastModifiedOn: folder?.updatedAt
                    ? format(new Date(folder.updatedAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a")
                    : format(new Date(folder.createdAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"),
                fileSize: '-',
                isSelected: selectedItems.includes(folder.id),
            });
        });

        // Add files to table data
        files.forEach((file) => {
            const extension = file.file_name.split('.').pop()?.toLowerCase();
            let nameIcon = <FileAssetsIcon />;
            if (['mov', 'mp4', 'avi', 'mkv'].includes(extension || '')) {
                nameIcon = <VideoAssetsIcon />;
            }

            transformedItems.push({
                id: file.id,
                type: 'file',
                originalData: file,
                nameIcon,
                name: file.file_name,
                ownerIcon: '/assets-crm/assets/img/Owner.png',
                owner: 'Me',
                lastModifiedByIcon: '/assets-crm/assets/img/LastModified.png',
                lastModifiedBy: 'Me',
                lastModifiedOn: format(new Date(file.createdAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"),
                fileSize: formatFileSize(file.file_size),
                isSelected: selectedItems.includes(file.id),
            });
        });

        return transformedItems;
    }, [folders, files, selectedItems]);

    const recordsTotal = transformedData.length;

    return (
        <>
            <DataTable
                isMultiselect={true}
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={transformedData}
                fixedPosition="first right"
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
            />
        </>
    );
};

export default ListView;