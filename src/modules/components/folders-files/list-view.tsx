import { DataTable, TableFooter } from "@dilpesh/kgk-ui-library";
import { useState } from "react";
import { assetsListingData, assetsListingHead } from "../../utils/constant";
import { getColoumnAssetListing } from "../../utils/utility";

// type Props = {}

const ListView = (props: any) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [currentPageSize, setCurrentPageSize] = useState(7);
    const [recordsTotal, setRecordsTotal] = useState(300);
    const coloumnDefAssetsListing = getColoumnAssetListing({})
    return (
        <>
            <DataTable
                isMultiselect={true}
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={assetsListingData}
                fixedPosition="first right"
            />
            <TableFooter
                currentPageSize={currentPageSize}
                currentPage={currentPage}
                recordsTotal={recordsTotal}
                setCurrentPage={setCurrentPage}
                setCurrentPageSize={setCurrentPageSize}
            />
        </>
    )
};

export default ListView;