"use client"
import { FileAssetsIcon, FolderAssetsIcon, VideoAssetsIcon } from "@/assets/icons";
import { DataTable } from "@dilpesh/kgk-ui-library";
import { getSessionItem, SESSION_KEYS } from "@magneto-it-solutions/kgk-common-library";
import { format } from "date-fns";
import { assetsListingHead } from "../../utils/constant";
import { getColoumnAssetListing } from "../../utils/utility";

interface Folder {
    _id: string;
    folder_name: string;
    folder_slug: string;
    parent_folder_id: string | null;
    createdAt: string;
    updatedAt: string | null;
    is_public: boolean;
}

interface File {
    _id: string;
    file_name: string;
    file_size: number;
    parent_folder_id: string | null;
    createdAt: string;
    file_reference_id: string;
    path: string;
}

interface ListViewProps {
    folders: Folder[];
    files: File[];
    onFolderClick: (folder: Folder) => void;
    onFileClick: (file: File) => void;
}

const ListView: React.FC<ListViewProps> = ({
    folders,
    files,
    onFolderClick,
    onFileClick
}) => {
    console.log('folders', folders);
    console.log('files', files);


    // Format file size helper
    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    // Get column definition
    const coloumnDefAssetsListing = getColoumnAssetListing({ onFolderClick, onFileClick });

    // Transform API data to table format
    const transformedData: any[] = [];

    // Add folders to table data
    folders.forEach((folder) => {
        transformedData.push({
            id: folder._id,
            type: 'folder',
            originalData: folder,
            nameIcon: <FolderAssetsIcon />,
            name: folder.folder_name,
            ownerIcon: '/assets-crm/assets/img/Owner.png',
            owner: 'Me',
            lastModifiedByIcon: '/assets-crm/assets/img/LastModified.png',
            lastModifiedBy: 'Me',
            lastModifiedOn: folder?.updatedAt
                ? format(new Date(folder.updatedAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a")
                : format(new Date(folder.createdAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"),
            fileSize: '-',
        });
    });

    // Add files to table data
    files.forEach((file) => {
        const extension = file.file_name.split('.').pop()?.toLowerCase();
        let nameIcon = <FileAssetsIcon />;
        if (['mov', 'mp4', 'avi', 'mkv'].includes(extension || '')) {
            nameIcon = <VideoAssetsIcon />;
        }

        transformedData.push({
            id: file._id,
            type: 'file',
            originalData: file,
            nameIcon,
            name: file.file_name,
            ownerIcon: '/assets-crm/assets/img/Owner.png',
            owner: 'Me',
            lastModifiedByIcon: '/assets-crm/assets/img/LastModified.png',
            lastModifiedBy: 'Me',
            lastModifiedOn: format(new Date(file.createdAt), getSessionItem(SESSION_KEYS.DATE_FORMAT) || "dd MMM yyyy, h:mm a"),
            fileSize: formatFileSize(file.file_size),
        });
    });

    return (
        <>
            <DataTable
                column={assetsListingHead}
                coloumnDef={coloumnDefAssetsListing}
                data={transformedData}
                fixedPosition="first right"
            />

        </>
    );
};

export default ListView;