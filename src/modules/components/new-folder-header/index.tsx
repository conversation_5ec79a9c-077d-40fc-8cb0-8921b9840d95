import { WhitePlus } from "@/assets/icons";
import { Btn, CustomeDropdown } from "@dilpesh/kgk-ui-library";
import { itemsForList } from "../../utils/constant";

interface NewFolderHeaderProps {
    onNewFolderClick?: () => void;
}

const NewFolderHeader: React.FC<NewFolderHeaderProps> = ({ onNewFolderClick }) => {

    const newFileItems = [
        {
            key: 1,
            value: "New Folder",
            class: 'black',
            onClick: onNewFolderClick
        },
        {
            key: 2,
            value: "Upload Files",
            class: 'black',
            onClick: () => {
                // Handle file upload
                console.log('Upload files clicked');
            }
        }
    ];

    return (
        <CustomeDropdown
            items={itemsForList}
            actionList={newFileItems}
            overlayClassName="icon_dropdown"
        >
            <Btn bg="fill" size="large"><WhitePlus />New</Btn>
        </CustomeDropdown>
    )
};

export default NewFolderHeader;