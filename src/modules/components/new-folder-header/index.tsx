import { WhitePlus } from "@/assets/icons";
import { Btn, CustomeDropdown } from "@dilpesh/kgk-ui-library";
import { itemsForList, newFileItems } from "../../utils/constant";

type Props = {}

const NewFolderHeader = (props: Props) => {
    return (
        <CustomeDropdown
            items={itemsForList}
            actionList={newFileItems}
            overlayClassName="icon_dropdown"
        >
            <Btn bg="fill" size="large"><WhitePlus />New</Btn>
        </CustomeDropdown>
    )
};

export default NewFolderHeader;