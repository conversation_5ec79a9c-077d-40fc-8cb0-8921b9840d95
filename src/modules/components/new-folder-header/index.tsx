import { FileUploadIcon, FolderUploadIcon, NewFolder, WhitePlus } from "@/assets/icons";
import { Btn, CustomeDropdown } from "@dilpesh/kgk-ui-library";
import { itemsForList } from "../../utils/constant";

interface NewFolderHeaderProps {
    onNewFolderClick?: () => void;
}

const NewFolderHeader: React.FC<NewFolderHeaderProps> = ({ onNewFolderClick }) => {

    const newFileItems = [
        {
            key: 1,
            value: <>
                <NewFolder />
                <span>New folder</span>
            </>,
            onClick: onNewFolderClick,
            class: 'icon_text_24_bs'
        },
        {
            type: 'divider',
        },
        {
            key: 2,
            value: <>
                <FileUploadIcon />
                <span>File upload</span>
            </>,
            class: 'icon_text_24_bs'
        },
        {
            key: 3,
            value: <>
                <FolderUploadIcon />
                <span>Folder upload</span>
            </>,
            class: 'icon_text_24_bs'
        },
    ];

    return (
        <CustomeDropdown
            items={itemsForList}
            actionList={newFileItems}
            overlayClassName="icon_dropdown"
        >
            <Btn bg="fill" size="large"><WhitePlus />New</Btn>
        </CustomeDropdown>
    )
};

export default NewFolderHeader;