import { GridIcon, ListIcon } from "@/assets/icons";
import { Btn, SquareBtnGroup } from "@dilpesh/kgk-ui-library";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { useState } from "react";
import { AssetsSettingModalStyle } from "./assets-setting-modal.style";

export default function AssetsSettingModal({
    show,
    onHide,
    rejectBtnEvent,
}: any) {

    const data = [
        {
            value: 'grid',
            content: <GridIcon />
        },
        {
            value: 'listing',
            content: <ListIcon />
        }
    ];

    const [activeBtn, setActivebtn] = useState(data[0].value);


    return (
        <AssetsSettingModalStyle
            title={'Setting'}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal assets_setting_modal"
            footer={[
                <Btn onClick={rejectBtnEvent} size="large">
                    Cancel
                </Btn>,
                <Btn bg="fill" size="large">
                    Save
                </Btn>,
            ]}
        >
            <div className="upper_storedata">
                <h3>Storage</h3>
                <div className="progress_bar">
                    <span className="progress_bar_fill" style={{ width: "70%" }}></span>
                </div>
                <p>4 GB of 5 GB used</p>
            </div>
            <div className="mode_view">
                <h4>Default view</h4>
                <SquareBtnGroup
                    data={data}
                    value={activeBtn}
                    onChange={(e: any) => setActivebtn(e.target.value)}
                />
            </div>
        </AssetsSettingModalStyle>
    )
}