import { Btn } from "@dilpesh/kgk-ui-library";
// import { default as AccessUserOwner } from "../../../../assets/icons/assets-page/access_user1.svg";
// import { default as AccessUser2 } from "../../../../assets/icons/assets-page/access_user2.svg";
// import { default as AccessUser3 } from "../../../../assets/icons/assets-page/access_user3.svg";
// import CloseIcon from "../../../../assets/icons/modal-close-icon.svg";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { AssetsInfoModalStyle } from "./assets-info-modal.style";

export default function AssetsInfoModal({
    show,
    onHide,
    rejectBtnEvent,
}: any) {

    const fileDetailsData = [
        {
            title: 'Type',
            value: 'Image'
        },
        {
            title: 'Size',
            value: '1.5 MB'
        },
        {
            title: 'Location',
            value: '1.5 MB'
        },
        {
            title: 'Owner',
            value: 'Me'
        },
        {
            title: 'Modified',
            value: '29 Mar, 2023, 03:00 PM'
        },
        {
            title: 'Created',
            value: '29 Mar, 2023, 03:00 PM'
        },
    ]

    return (
        <AssetsInfoModalStyle
            title={'KGK_Logo_Black.JPEG'}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal assets_info_modal"
            footer={null}
        >
            <div className="inner_section_wrap">
                <div className="top_section">
                    <div className="img_wpr">
                        <img src="/assets-crm/assets/img/modalinfo_image.png" alt="" />
                    </div>
                    <div className="access_users">
                        <h3>Who has access</h3>
                        {/* <div className="image_wrap">
                            <div className="owner_img">
                                <AccessUserOwner />
                            </div>
                            <div className="shared_img">
                                <AccessUser2 />
                                <AccessUser3 />
                            </div>
                        </div> */}
                        <p className="own_names">Owned by you. Shared with James White and Alena Smith.</p>
                        <div className="action_wrap">
                            <Btn bg={"fill"} size={"large"}>Manage access</Btn>
                        </div>
                    </div>
                </div>
                <hr className="infomodal_separator" />
                <div className="bottom_desc">
                    <h3>File details</h3>
                    <table>
                        {
                            fileDetailsData?.map((item: any) => (
                                <tr>
                                    <td>{item.title}</td>
                                    <td>{item.value}</td>
                                </tr>
                            ))
                        }
                    </table>
                </div>
            </div>
            <form className="body">

            </form>
        </AssetsInfoModalStyle>
    )
}
