import { Btn, InputGroups } from "@dilpesh/kgk-ui-library";
import { CloseIcon, useCreateFolder } from "@magneto-it-solutions/kgk-common-library";
import { Col, Row } from "antd";
import { AssetsNewFolderModalStyle } from "./assets-new-folder-modal.style";

export default function AssetsNewFolderModal({
    show,
    onHide,
    rejectBtnEvent,
}: any) {
    const { createFolder, isLoading } = useCreateFolder()
    return (
        <AssetsNewFolderModalStyle
            title={'New folder'}
            centered
            closeIcon={<CloseIcon />}
            open={show}
            onOk={onHide}
            onCancel={onHide}
            width={464}
            wrapClassName="detail_modal"
            footer={[
                <Btn onClick={rejectBtnEvent} size="large">
                    Cancel
                </Btn>,
                <Btn bg="fill" size="large">
                    Create
                </Btn>,
            ]}
        >
            <form className="body">
                <Row>
                    <Col xs={24}>
                        <InputGroups
                            label="Folder name"
                        />
                    </Col>
                </Row>
            </form>
        </AssetsNewFolderModalStyle>
    )
}
