import { DownArrowIcon, FileListGallery } from "@/assets/icons";
import { CloseIcon } from "@magneto-it-solutions/kgk-common-library";
import { Progress } from "antd";
import { useState } from "react";

type Props = {
    leftPosition: string
}

const UploadProgress = (props: Props) => {
    const [uploadingItem, setuploadingItem] = useState(false);
    const [hover, setHover] = useState(false);

    const handleUploadToggle = () => {
        setuploadingItem((current) => !current);
    };

    return (
        <div className="uploading_wpr" style={{ right: props?.leftPosition }}>
            <div className={`heading_wpr ${uploadingItem ? 'active' : ''}`}>
                <p>Uploading 1 item</p>
                <div className="action_wpr">
                    <DownArrowIcon onClick={handleUploadToggle} className="arrow" />
                    <CloseIcon />
                </div>
            </div>
            <div className={`innerdata_wpr ${uploadingItem ? 'active' : 'close'}`}>
                <div className="item_box" onMouseEnter={() => setHover(true)} onMouseLeave={() => setHover(false)}>
                    <div className="text_wrap">
                        <FileListGallery />
                        <p>img1254698.JPG</p>
                    </div>
                    <div className="progress_wpr">
                        {
                            hover ? <CloseIcon /> : <Progress type="circle" strokeWidth={16} percent={75} size={24} showInfo={false} trailColor="#C5C5C5" strokeColor="#383230" />
                        }
                    </div>
                </div>
            </div>
        </div>
    )
};

export default UploadProgress;