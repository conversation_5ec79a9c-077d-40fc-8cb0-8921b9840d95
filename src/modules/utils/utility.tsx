"use client"
import { ActionIcon, DownloadIcon, ShareIcon } from "@/assets/icons";
import { ActionDropdown } from "@dilpesh/kgk-ui-library";
import { EditIcon, LinkIcon } from "@magneto-it-solutions/kgk-common-library";


export const getColoumnAssetListing = ({ onFolderClick, onFileClick }: any) => {
    return {
        name: {
            renderChildren: (item: any) => {
                const handleClick = () => {
                    if (item.type === 'folder' && onFolderClick) {
                        onFolderClick(item.originalData);
                    } else if (item.type === 'file' && onFileClick) {
                        onFileClick(item.originalData);
                    }
                };

                return (
                    <div
                        className='icon_name_24_12_bes'
                        onClick={handleClick}
                        style={{ cursor: 'pointer' }}
                    >
                        {item.nameIcon}
                        <p>{item.name}</p>
                    </div>
                );
            },
        },
        owner: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes with_radius'>
                        <img src={item.ownerIcon} alt="" />
                        <p>{item.owner}</p>
                    </div>
                );
            },
        },
        lastModifiedBy: {
            renderChildren: (item: any) => {
                return (
                    <div className='icon_name_24_8_bes with_radius'>
                        <img src={item.lastModifiedByIcon} alt="" />
                        <p>{item.lastModifiedBy}</p>
                    </div>
                );
            },
        },
        action: {
            renderChildren: (item: any) => {
                const actionitemList = [
                    {
                        key: 1,
                        value: 'View users',
                        class: 'black',
                    },
                    {
                        key: 2,
                        value: 'Mark as Inactive',
                        class: 'black'
                    },
                ];
                return (
                    <div className='action_wpr'>
                        <a><ShareIcon /></a>
                        <a><LinkIcon /></a>
                        <a><DownloadIcon /></a>
                        <a><EditIcon /></a>
                        <ActionDropdown
                            items={{
                                dropId: 'super-action'
                            }}
                            actionList={actionitemList}
                            actionIcon={<ActionIcon />}
                        />
                    </div>
                );
            },
        },
    };
}
