"use client"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SortBy, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useGetFolderDataQuery } from "@magneto-it-solutions/kgk-common-library";
import { useLayoutEffect, useRef, useState } from "react";
import FilterOptions from "../../modules/components/filter-options";
import FoldersFiles from "../../modules/components/folders-files";
import NewFolderHeader from "../../modules/components/new-folder-header";
import Selectedheader from "../../modules/components/selected-header";
import UploadProgress from "../../modules/components/upoload-progress";
import { AssetsPageStyle } from "../../modules/style";
import { listViewOptions, pageStore, sortOptions } from "../../modules/utils/constant";


export default function AssetsPage() {

    const ref: any = useRef();

    const [leftPosition, setleftPosition] = useState('');
    const [sortOpen, setSortOpen] = useState(false)
    const [activeBtn, setActivebtn] = useState(listViewOptions[0].value);
    const { data: folderData, isLoading, isError, error } = useGetFolderDataQuery('');

    useLayoutEffect(() => {
        setleftPosition(ref.current.offsetLeft + 'px');
    }, []);


    return (
        <>
            <AssetsPageStyle className="assets_landing_page">
                <div className="container">
                    <div className="findLeft" ref={ref}></div>
                    <div className="breadcrumb_wrap">
                        <BreadCrumbs value={pageStore}></BreadCrumbs>
                    </div>
                    <div className="title_wrap">
                        <h1>Assets</h1>
                    </div>
                    <TabletFilterBtn
                        sort={true}
                        sortClick={() => { setSortOpen(true) }}
                    />
                    <div className="asset_content_wrap">
                        <div className="subheader_wrap">
                            {false ?
                                <Selectedheader />
                                :
                                <NewFolderHeader />
                            }
                            <FilterOptions
                                setActivebtn={setActivebtn}
                                activeBtn={activeBtn}
                            />
                        </div>
                        <SortBy
                            options={sortOptions}
                            defaultValue={sortOptions[0].value}
                            open={sortOpen}
                            close={() => { setSortOpen(false) }}
                        />
                        <FoldersFiles
                            activeBtn={activeBtn}
                            folderData={folderData}
                        />
                    </div>
                    <UploadProgress leftPosition={leftPosition} />
                </div>
            </AssetsPageStyle >

        </>
    )
}