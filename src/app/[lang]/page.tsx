"use client"
import { B<PERSON><PERSON>rum<PERSON>, SortBy, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useGetFolderDataQuery } from "@magneto-it-solutions/kgk-common-library";
import { useSearchParams } from "next/navigation";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import FilterOptions from "../../modules/components/filter-options";
import FoldersFiles from "../../modules/components/folders-files";
import NewFolderHeader from "../../modules/components/new-folder-header";
import Selectedheader from "../../modules/components/selected-header";
import UploadProgress from "../../modules/components/upoload-progress";
import { AssetsPageStyle } from "../../modules/style";
import { listViewOptions, sortOptions } from "../../modules/utils/constant";

interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

interface BreadcrumbItem {
    link?: string;
    title: string;
    folderId?: string;
}

export default function AssetsPage() {

    const ref: any = useRef();
    const searchParams = useSearchParams();

    const [leftPosition, setleftPosition] = useState('');
    const [sortOpen, setSortOpen] = useState(false)
    const [activeBtn, setActivebtn] = useState(listViewOptions[0].value);
    const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
        {
            link: 'Home',
            title: 'Home'
        },
        {
            title: 'Assets'
        }
    ]);

    // Get folder ID from URL parameters
    const currentFolderId = searchParams.get('folderId') || '';
    const { data: folderData, isLoading, isError } = useGetFolderDataQuery(currentFolderId);

    // // Show loading state
    // if (isLoading) {
    //     return (
    //         <AssetsPageStyle className="assets_landing_page">
    //             <div className="container">
    //                 <div className="title_wrap">
    //                     <h1>Loading...</h1>
    //                 </div>
    //             </div>
    //         </AssetsPageStyle>
    //     );
    // }

    // // Show error state
    // if (isError) {
    //     return (
    //         <AssetsPageStyle className="assets_landing_page">
    //             <div className="container">
    //                 <div className="title_wrap">
    //                     <h1>Error loading data</h1>
    //                     <p>{'Something went wrong'}</p>
    //                 </div>
    //             </div>
    //         </AssetsPageStyle>
    //     );
    // }

    useLayoutEffect(() => {
        setleftPosition(ref.current.offsetLeft + 'px');
    }, []);

    // Reset breadcrumbs when navigating back to root
    useEffect(() => {
        if (!currentFolderId) {
            setBreadcrumbs([
                {
                    link: 'Home',
                    title: 'Home'
                },
                {
                    title: 'Assets'
                }
            ]);
        }
    }, [currentFolderId]);

    // Handle folder navigation and breadcrumb updates
    const handleFolderNavigate = (folder: Folder) => {
        // Add the folder to breadcrumbs
        setBreadcrumbs(prev => [
            ...prev,
            {
                title: folder.name,
                folderId: folder.id
            }
        ]);
    };

    return (
        <>
            <AssetsPageStyle className="assets_landing_page">
                <div className="container">
                    <div className="findLeft" ref={ref}></div>
                    <div className="breadcrumb_wrap">
                        <BreadCrumbs value={breadcrumbs} />
                    </div>
                    <div className="title_wrap">
                        <h1>Assets</h1>
                    </div>
                    <TabletFilterBtn
                        sort={true}
                        sortClick={() => { setSortOpen(true) }}
                    />
                    <div className="asset_content_wrap">
                        <div className="subheader_wrap">
                            {false ?
                                <Selectedheader />
                                :
                                <NewFolderHeader />
                            }
                            <FilterOptions
                                setActivebtn={setActivebtn}
                                activeBtn={activeBtn}
                            />
                        </div>
                        <SortBy
                            options={sortOptions}
                            defaultValue={sortOptions[0].value}
                            open={sortOpen}
                            close={() => { setSortOpen(false) }}
                        />
                        <FoldersFiles
                            activeBtn={activeBtn}
                            folderData={folderData}
                            onFolderNavigate={handleFolderNavigate}
                        />
                    </div>
                    <UploadProgress leftPosition={leftPosition} />
                </div>
            </AssetsPageStyle >

        </>
    )
}