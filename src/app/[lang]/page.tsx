"use client"
import AssetsInfoModal from "@/modules/components/modal/assets-info-modal/assets-info-modal";
import AssetsMoveModal from "@/modules/components/modal/assets-move-modal/assets-move-modal";
import AssetsNewFolderModal from "@/modules/components/modal/assets-new-folder-modal/assets-new-folder-modal";
import AssetsSettingModal from "@/modules/components/modal/assets-setting-modal/assets-setting-modal";
import AssetsShareModal from "@/modules/components/modal/assets-share-modal/assets-share-modal";
import { BreadCrumbs, SortBy, TabletFilterBtn } from "@dilpesh/kgk-ui-library";
import { useGetFolderDataQuery } from "@magneto-it-solutions/kgk-common-library";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import FilterOptions from "../../modules/components/filter-options";
import FoldersFiles from "../../modules/components/folders-files";
import NewFolderHeader from "../../modules/components/new-folder-header";
import Selectedheader from "../../modules/components/selected-header";
import UploadProgress from "../../modules/components/upoload-progress";
import { AssetsPageStyle } from "../../modules/style";
import { listViewOptions, sortOptions } from "../../modules/utils/constant";
interface Folder {
    id: string;
    name: string;
    slug: string;
    parentFolderId: string | null;
    createdAt: string;
    updatedAt: string | null;
    isPublic: boolean;
}

interface BreadcrumbItem {
    link?: string;
    title: string;
    folderId?: string;
}

export default function AssetsPage() {

    const ref: any = useRef();
    const router = useRouter();
    const searchParams = useSearchParams();
    const [leftPosition, setleftPosition] = useState('');
    const [sortOpen, setSortOpen] = useState(false)
    const [activeBtn, setActivebtn] = useState(listViewOptions[0].value);
    const [selectedItems, setSelectedItems] = useState<string[]>([]);

    const [anfModalOpen, setanfModalOpen] = useState(false);
    const [aiModalOpen, setaiModalOpen] = useState(false);
    const [asModalOpen, setasModalOpen] = useState(false);
    const [amModalOpen, setamModalOpen] = useState(false);
    const [asettingModalOpen, setasettingModalOpen] = useState(false);

    const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
        {
            link: 'Home',
            title: 'Home'
        },
        {
            title: 'Assets'
        }
    ]);

    // Get folder ID from URL parameters
    const currentFolderId = searchParams.get('folderId') || '';
    const { data: folderData, isLoading, isError } = useGetFolderDataQuery(currentFolderId);

    useLayoutEffect(() => {
        setleftPosition(ref.current.offsetLeft + 'px');
    }, []);

    // Reset breadcrumbs when navigating back to root
    useEffect(() => {
        if (!currentFolderId) {
            setBreadcrumbs([
                {
                    link: 'Home',
                    title: 'Home'
                },
                {
                    title: 'Assets',
                    link: '/assets-crm'
                }
            ]);
        }
    }, [currentFolderId]);

    // Handle folder navigation and breadcrumb updates
    const handleFolderNavigate = (folder: Folder) => {
        const params = new URLSearchParams(searchParams.toString());
        params.set('folderId', folder.id);
        router.push(`?${params.toString()}`);
        // Add the folder to breadcrumbs
        setBreadcrumbs(prev => [
            ...prev,
            {
                title: folder.name,
                folderId: folder.id
            }
        ]);
    };

    return (
        <>
            <AssetsPageStyle className="assets_landing_page">
                <div className="container">
                    <div className="findLeft" ref={ref}></div>
                    <div className="breadcrumb_wrap">
                        <BreadCrumbs value={breadcrumbs} />
                    </div>
                    <div className="title_wrap">
                        <h1>Assets</h1>
                    </div>
                    <TabletFilterBtn
                        sort={true}
                        sortClick={() => { setSortOpen(true) }}
                    />
                    <div className="asset_content_wrap">
                        <div className="subheader_wrap">
                            {selectedItems.length > 0 ?
                                <Selectedheader />
                                :
                                <NewFolderHeader onNewFolderClick={() => setanfModalOpen(true)} />
                            }
                            <FilterOptions
                                setActivebtn={setActivebtn}
                                activeBtn={activeBtn}
                            />
                        </div>
                        <SortBy
                            options={sortOptions}
                            defaultValue={sortOptions[0].value}
                            open={sortOpen}
                            close={() => { setSortOpen(false) }}
                        />
                        <FoldersFiles
                            viewMode={activeBtn}
                            folderData={folderData}
                            selectedItems={selectedItems}
                            onFolderNavigate={handleFolderNavigate}
                            onItemSelect={(itemId: string) => {
                                setSelectedItems(prev =>
                                    prev.includes(itemId)
                                        ? prev.filter(id => id !== itemId)
                                        : [...prev, itemId]
                                );
                            }}
                        />
                    </div>
                    <UploadProgress leftPosition={leftPosition} />
                </div>
            </AssetsPageStyle >
            {anfModalOpen && (
                <AssetsNewFolderModal
                    onHide={() => setanfModalOpen(false)}
                    show={anfModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setanfModalOpen(false)}
                />
            )}

            {aiModalOpen && (
                <AssetsInfoModal
                    onHide={() => setaiModalOpen(false)}
                    show={aiModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setaiModalOpen(false)}
                />
            )}

            {asModalOpen && (
                <AssetsShareModal
                    onHide={() => setasModalOpen(false)}
                    show={asModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setasModalOpen(false)}
                />
            )}

            {asettingModalOpen && (
                <AssetsSettingModal
                    onHide={() => setasettingModalOpen(false)}
                    show={asettingModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setasettingModalOpen(false)}
                />
            )}

            {amModalOpen && (
                <AssetsMoveModal
                    onHide={() => setamModalOpen(false)}
                    show={amModalOpen}
                    approveBtnEvent={() => { }}
                    rejectBtnEvent={() => setamModalOpen(false)}
                />
            )}


        </>
    )
}